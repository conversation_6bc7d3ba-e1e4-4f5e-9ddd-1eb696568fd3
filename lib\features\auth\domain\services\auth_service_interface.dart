import 'package:stackfood_multivendor/common/models/response_model.dart';
import 'package:stackfood_multivendor/features/auth/domain/models/signup_body_model.dart';
import 'package:stackfood_multivendor/features/auth/domain/models/social_log_in_body_model.dart';

abstract class AuthServiceInterface{
  Future<ResponseModel> registration(SignUpBodyModel signUpModel);
  Future<ResponseModel> login({required String emailOrPhone, required String password, required String loginType, required String fieldType, bool alreadyInApp = false});
  Future<ResponseModel> otpLogin({required String phone, required String otp, required String loginType, required String verified, bool alreadyInApp = false});
  Future<ResponseModel> updatePersonalInfo({required String name, required String? phone, required String loginType, required String? email, required String? referCode, bool alreadyInApp = false});
  String getUserCountryCode();
  String getUserNumber();
  String getUserPassword();
  void saveUserNumberAndPassword({required String number, required String password, required String countryCode, required String otpPoneNumber});
  Future<bool> clearUserNumberAndPassword();
  Future<ResponseModel> guestLogin();
  Future<ResponseModel> loginWithSocialMedia(SocialLogInBodyModel socialLogInModel, {bool isCustomerVerificationOn = false});
  Future<void> updateToken();
  bool isLoggedIn();
  String getGuestId();
  bool isGuestLoggedIn();
  Future<void> socialLogout();
  Future<bool> clearSharedData({bool removeToken = true});
  Future<bool> setNotificationActive(bool isActive);
  bool isNotificationActive();
  String getUserToken();
  Future<void> saveGuestNumber(String number);
  String getGuestNumber();
  String getUserOtpPhoneNumber();
}