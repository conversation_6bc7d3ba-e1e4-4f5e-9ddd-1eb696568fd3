enum NotificationType{
  message,
  order,
  general,
  //ignore: constant_identifier_names
  referral_code,
  //ignore: constant_identifier_names
  referral_earn,
  //ignore: constant_identifier_names
  CashBack,
  block,
  unblock,
  // ignore: constant_identifier_names
  add_fund,
}

class NotificationBodyModel {
  NotificationType? notificationType;
  int? orderId;
  int? adminId;
  int? deliverymanId;
  int? restaurantId;
  String? type;
  int? conversationId;
  int? index;
  String? image;
  String? name;
  String? receiverType;

  NotificationBodyModel({
    this.notificationType,
    this.orderId,
    this.adminId,
    this.deliverymanId,
    this.restaurantId,
    this.type,
    this.conversationId,
    this.index,
    this.image,
    this.name,
    this.receiverType,
  });

  NotificationBodyModel.fromJson(Map<String, dynamic> json) {
    notificationType = convertToEnum(json['order_notification']);
    orderId = json['order_id'];
    adminId = json['admin_id'];
    deliverymanId = json['deliveryman_id'];
    restaurantId = json['restaurant_id'];
    type = json['type'];
    conversationId = json['conversation_id'];
    index = json['index'];
    image = json['image'];
    name = json['name'];
    receiverType = json['receiver_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['order_notification'] = notificationType.toString();
    data['order_id'] = orderId;
    data['admin_id'] = adminId;
    data['deliveryman_id'] = deliverymanId;
    data['restaurant_id'] = restaurantId;
    data['type'] = type;
    data['conversation_id'] = conversationId;
    data['index'] = index;
    data['image'] = image;
    data['name'] = name;
    data['receiver_type'] = receiverType;
    return data;
  }

  NotificationType convertToEnum(String? enumString) {
    if(enumString == NotificationType.general.toString()) {
      return NotificationType.general;
    }else if(enumString == NotificationType.order.toString()) {
      return NotificationType.order;
    }else if(enumString == NotificationType.message.toString()) {
      return NotificationType.message;
    } else if(enumString == NotificationType.referral_code.toString()) {
      return NotificationType.referral_code;
    }else if(enumString == NotificationType.referral_earn.toString()) {
      return NotificationType.referral_earn;
    }else if(enumString == NotificationType.CashBack.toString()) {
      return NotificationType.CashBack;
    }else if(enumString == NotificationType.block.toString()) {
      return NotificationType.block;
    }else if(enumString == NotificationType.unblock.toString()) {
      return NotificationType.unblock;
    }else if(enumString == NotificationType.add_fund.toString()) {
      return NotificationType.add_fund;
    }
    return NotificationType.general;
  }

}
