name: stackfood_multivendor
description: A new Flutter application.
publish_to: 'none' #
version: 1.0.0+1

environment:
  sdk: ^3.4.4

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  get: ^4.6.6
  shared_preferences: ^2.3.4
  connectivity_plus: ^6.0.5
  firebase_core: ^3.8.0
  firebase_messaging: ^15.1.5
  flutter_local_notifications: ^17.2.3
  path_provider: ^2.1.4
  url_strategy: ^0.3.0
  pin_code_fields: ^8.0.1
  geolocator: ^13.0.1
  google_maps_flutter: ^2.9.0
  google_maps_flutter_web: ^0.5.10
  carousel_slider: ^5.0.0
  shimmer_animation: ^2.2.1
  image_picker: ^1.1.2
  universal_html: ^2.2.4
  flutter_inappwebview: ^6.1.4
  url_launcher: ^6.3.0
  http: ^1.2.2
  pointer_interceptor: ^0.10.1+2
  phone_numbers_parser: ^9.0.1
  country_code_picker: ^3.0.0
  cached_network_image: ^3.4.1
  google_sign_in: ^6.2.1
  google_sign_in_web: ^0.12.4+2
  flutter_facebook_auth: ^6.0.4
  photo_view: ^0.15.0
  dotted_border: ^2.1.0
  share_plus: ^10.0.2
  custom_map_markers: ^0.0.2+1
#  image_compression_flutter: ^1.0.4
  card_swiper: ^3.0.1
  expandable_bottom_sheet: ^1.1.1+1
#  uni_links: ^0.5.1
  sign_in_with_apple: ^6.1.1
  intl: ^0.19.0
  flutter_slidable: ^3.1.2
  just_the_tooltip: ^0.0.12
  animated_flip_counter: ^0.3.4
  meta_seo: ^3.0.9
  marquee: ^2.2.3
  syncfusion_flutter_datepicker: ^27.1.51
#  firebase_crashlytics: ^4.0.4
  file_picker: ^8.1.2
  path: ^1.9.0
  http_parser: ^4.1.0
  flutter_svg: ^2.0.10+1
  custom_info_window: ^1.0.1
  flutter_widget_from_html_core: ^0.15.2
  smooth_page_indicator: ^1.2.0+3
  video_player: ^2.9.3
  chewie: ^1.11.0
  location: ^7.0.0
  permission_handler: ^11.3.1
  firebase_auth: ^5.3.1
  get_thumbnail_video: ^0.7.3

#  wakelock_plus: ^1.2.8
#  video_player_avfoundation: ^2.6.2
  drift: ^2.21.0
  drift_flutter: ^0.2.1
#  postgres: ^3.4.3
#  drift_postgres: ^1.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  drift_dev: ^2.21.2
  build_runner: ^2.4.13

dependency_overrides:
  fading_edge_scrollview: ^4.1.1
  collection: ^1.19.0
  web: ^1.1.0

flutter:
  uses-material-design: true

  assets:
    - assets/image/
    - assets/language/
    - assets/map/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/font/Roboto-Regular.ttf
          weight: 400
        - asset: assets/font/Roboto-Medium.ttf
          weight: 500
        - asset: assets/font/Roboto-Bold.ttf
          weight: 700
        - asset: assets/font/Roboto-Black.ttf
          weight: 900