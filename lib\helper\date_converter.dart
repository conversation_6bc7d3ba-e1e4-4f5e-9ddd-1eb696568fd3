import 'package:stackfood_multivendor/features/splash/controllers/splash_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class DateConverter {

  static String formatDate(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd hh:mm:ss a').format(dateTime);
  }

  static String dateToTimeOnly(DateTime dateTime) {
    return DateFormat(_timeFormatter()).format(dateTime);
  }

  static String dateToDateAndTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
  }

  static String dateToDateAndTimeAm(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd ${_timeFormatter()}').format(dateTime);
  }

  static String onlyDate(DateTime dateTime) {
    return DateFormat('dd-MM-yyyy').format(dateTime);
  }

  static String dateTimeStringToDateTime(String dateTime) {
    return DateFormat('dd MMM yyyy  ${_timeFormatter()}').format(DateFormat('yyyy-MM-dd HH:mm:ss').parse(dateTime));
  }

  static String dateTimeStringToDateTimeToLines(String dateTime) {
    return DateFormat('dd MMM yyyy  \n${_timeFormatter()}').format(DateFormat('yyyy-MM-dd HH:mm:ss').parse(dateTime));
  }

  static String dateTimeStringToDateOnly(String dateTime) {
    return DateFormat('dd MMM, yyyy').format(DateFormat('yyyy-MM-dd HH:mm:ss').parse(dateTime));
  }

  static String dateTimeStringToTime(String dateTime) {
    return DateFormat('HH:mm a').format(DateFormat('yyyy-MM-dd HH:mm:ss').parse(dateTime));
  }

  static String dateTimeStringToFormattedTime(String dateTime) {
    return DateFormat(_timeFormatter()).format(DateFormat('yyyy-MM-dd HH:mm:ss').parse(dateTime));
  }

  static String timeStringToTime(String dateTime) {
    return DateFormat('hh:mm a').format(DateFormat('HH:mm:ss').parse(dateTime));
  }

  static String stringToReadableString(String dateTime) {
    return DateFormat('dd MMMM, yyyy').format(DateTime.parse(dateTime).toLocal());
  }

  static String dateRangeToDate(DateTimeRange range) {
    return '${DateFormat('dd MMM, yyyy').format(range.start)} to ${DateFormat('dd MMM, yyyy').format(range.end)}';
  }

  static String stringDateTimeToDate(String dateTime) {
    return DateFormat('dd MMM yyyy').format(DateFormat('yyyy-MM-dd').parse(dateTime));
  }

  static String dateTimeToMonth(String dateTime) {
    return DateFormat('dd MMM').format(DateFormat('yyyy-MM-dd').parse(dateTime));
  }

  static DateTime dateTimeStringToDate(String dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').parse(dateTime);
  }

  static DateTime dateStringToDate(String dateTime) {
    return DateFormat('yyyy-MM-dd').parse(dateTime);
  }

  static DateTime isoStringToLocalDate(String dateTime) {
    return DateFormat('yyyy-MM-ddTHH:mm:ss.SSS').parse(dateTime);
  }

  static String isoStringToLocalString(String dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.parse(dateTime).toLocal());
  }

  static String isoStringToDateTimeString(String dateTime) {
    return DateFormat('dd MMM yyyy  ${_timeFormatter()}').format(isoStringToLocalDate(dateTime));
  }

  static String isoStringToLocalDateOnly(String dateTime) {
    return DateFormat('dd MMM yyyy').format(isoStringToLocalDate(dateTime));
  }

  static String isoStringToLocalDateTimeOnly(String dateTime) {
    return DateFormat('dd MMM yyyy HH:mm a').format(isoStringToLocalDate(dateTime));
  }

  static String dateTimeForCoupon(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd').format(dateTime);
  }

  static String stringToLocalDateOnly(String dateTime) {
    return DateFormat('dd MMM yyyy').format(DateFormat('yyyy-MM-dd').parse(dateTime));
  }

  static String stringToLocalDateDayOnly(String dateTime) {
    return DateFormat('dd').format(DateFormat('yyyy-MM-dd').parse(dateTime));
  }

  static String stringToLocalDateMonthAndYearOnly(String dateTime) {
    return DateFormat('MMM yy').format(DateFormat('yyyy-MM-dd').parse(dateTime));
  }

  static String localDateToIsoString(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  static String convertTimeToTime(String time) {
    return DateFormat(_timeFormatter()).format(DateFormat('HH:mm').parse(time));
  }

  static DateTime convertStringTimeToDate(String time) {
    return DateFormat('HH:mm').parse(time);
  }

  static String convertDateTimeToDate(DateTime time) {
    return DateFormat(_timeFormatter()).format(time);
  }

  static String localDateToIsoStringAMPM(DateTime dateTime) {
    return DateFormat('${_timeFormatter()} | d-MMM-yyyy ').format(dateTime.toLocal());
  }

  static String dateToTime(DateTime dateTime) {
    return DateFormat('HH:mm:ss').format(dateTime);
  }

  static bool isAvailable(String? start, String? end, {DateTime? time, bool isoTime = false}) {
    DateTime currentTime;
    if(time != null) {
      currentTime = time;
    }else {
      currentTime = Get.find<SplashController>().currentTime;
    }
    DateTime start0 = start != null ? isoTime ? isoStringToLocalDate(start) : DateFormat('HH:mm').parse(start) : DateTime(currentTime.year);
    DateTime end0 = end != null ? isoTime ? isoStringToLocalDate(end) : DateFormat('HH:mm').parse(end) : DateTime(currentTime.year, currentTime.month, currentTime.day, 23, 59);
    DateTime startTime = DateTime(currentTime.year, currentTime.month, currentTime.day, start0.hour, start0.minute, start0.second);
    DateTime endTime = DateTime(currentTime.year, currentTime.month, currentTime.day, end0.hour, end0.minute, end0.second);
    if(endTime.isBefore(startTime)) {
      if(currentTime.isBefore(startTime) && currentTime.isBefore(endTime)){
        startTime = startTime.add(const Duration(days: -1));
      }else {
        endTime = endTime.add(const Duration(days: 1));
      }
    }
    return currentTime.isAfter(startTime) && currentTime.isBefore(endTime);
  }

  static String _timeFormatter() {
    return Get.find<SplashController>().configModel!.timeformat == '24' ? 'HH:mm' : 'hh:mm a';
  }

  static int differenceInMinute(String? deliveryTime, String? orderTime, int? processingTime, String? scheduleAt, {bool fromDineIn = false, String? processing}) {
    // 'min', 'hours', 'days'
    if(fromDineIn && processingTime != null && processing != null) {
      DateTime deliveryTime0 = dateTimeStringToDate(processing).add(Duration(minutes: processingTime));
      return deliveryTime0.difference(DateTime.now()).inMinutes;
    }
    int minTime = processingTime ?? 0;
    if(deliveryTime != null && deliveryTime.isNotEmpty && processingTime == null) {
      try {
        List<String> timeList = deliveryTime.split('-'); // ['15', '20']
        minTime = int.parse(timeList[0]);
      }catch(_) {}
    }
    DateTime deliveryTime0 = dateTimeStringToDate(scheduleAt ?? orderTime!).add(Duration(minutes: minTime));
    return deliveryTime0.difference(DateTime.now()).inMinutes;
  }

  static bool isBeforeTime(String? dateTime) {
    if(dateTime == null) {
      return false;
    }
    DateTime scheduleTime = dateTimeStringToDate(dateTime);
    return scheduleTime.isBefore(DateTime.now());
  }

  static int getWeekDaysCount(DateTimeRange range, List<int> weekdays) {
    int quantity = 0;
    DateTime startDate = range.start;
    for(int index=0; index<(range.duration.inDays+1); index++) {
      if((startDate.isBefore(range.end) || startDate.isAtSameMomentAs(range.end)) && weekdays.contains(startDate.weekday)) {
        quantity++;
      }
      if(startDate.isAfter(range.end)) {
        break;
      }
      startDate = startDate.add(const Duration(days: 1));
    }
    return quantity;
  }

  static int getMonthDaysCount(DateTimeRange range, List<int> days) {
    int quantity = 0;
    DateTime startDate = range.start;
    for(int index=0; index<(range.duration.inDays+1); index++) {
      if((startDate.isBefore(range.end) || startDate.isAtSameMomentAs(range.end)) && days.contains(startDate.day)) {
        quantity++;
      }
      if(startDate.isAfter(range.end)) {
        break;
      }
      startDate = startDate.add(const Duration(days: 1));
    }
    return quantity;
  }

  static String containTAndZToUTCFormat(String time) {
    var newTime = '${time.substring(0,10)} ${time.substring(11,23)}';
    return DateFormat('dd MMM, yyyy').format(DateFormat('yyyy-MM-dd HH:mm:ss').parse(newTime));

    // return DateFormat('${_timeFormatter()} | d-MMM-yyyy ').format(dateTime.toLocal());
  }

  static String convertOnlyTodayTime(String createdAt) {
    final now = DateTime.now();
    final createdAtDate = DateTime.parse(createdAt).toLocal();

    if (createdAtDate.year == now.year &&
        createdAtDate.month == now.month &&
        createdAtDate.day == now.day) {
      return DateFormat('h:mm a').format(createdAtDate);
    } else {
      return DateConverter.localDateToIsoStringAMPM(createdAtDate);
    }
  }

  static DateTime isoUtcStringToLocalTimeOnly(String dateTime) {
    return DateFormat('yyyy-MM-ddTHH:mm:ss.SSS').parse(dateTime, true).toLocal();
  }

  static String isoStringToLocalDateAndTime(String dateTime) {
    return DateFormat('dd MMM yyyy \'at\' ${_timeFormatter()}').format(isoUtcStringToLocalTimeOnly(dateTime));
  }

  static int countDays(DateTime ? dateTime) {
    final startDate = dateTime!;
    final endDate = DateTime.now();
    final difference = endDate.difference(startDate).inDays;
    return difference;
  }

  static String convert24HourTimeTo12HourTimeWithDay(DateTime time, bool isToday) {
    if(isToday){
      return DateFormat('\'Today at\' ${_timeFormatter()}').format(time);
    }else{
      return DateFormat('\'Yesterday at\' ${_timeFormatter()}').format(time);
    }
  }

  static String convertStringTimeToDateTime (DateTime time){
    return DateFormat('EEE \'at\' ${_timeFormatter()}').format(time.toLocal());
  }

  static String convertRestaurantOpenTime(String time) {
    return DateFormat('hh:mm a').format(DateFormat('HH:mm:ss').parse(time).toLocal());
  }

  static DateTime formattingDineInDateTime(TimeOfDay pickedTime, DateTime selectedDineInDate) {
    String formattedTime = "${pickedTime.hour.toString().padLeft(2, '0')}:${pickedTime.minute.toString().padLeft(2, '0')}";
    List<String> times = formattedTime.split(':');

    return DateTime(selectedDineInDate.year, selectedDineInDate.month, selectedDineInDate.day, int.parse(times[0]), int.parse(times[1]));

  }

  static bool isToday(DateTime date1) {
    DateTime date2 = DateTime.now();
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
  static bool isTomorrow(DateTime date1) {
    DateTime date2 = DateTime.now().add(Duration(days: 1));
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  static String convertMinutesToDayHourMinute(int minutes) {
    int days = minutes ~/ 1440; // 1 day = 1440 minutes
    int hours = (minutes % 1440) ~/ 60; // Remaining hours
    int remainingMinutes = minutes % 60; // Remaining minutes
    return '${days != 0 ? '${days}d' : ''} ${hours != 0 ? '${hours}hr' : ''} ${remainingMinutes != 0 ? '${remainingMinutes}min' : ''}'; // Format as "Xd Yh Zm"
  }



}
