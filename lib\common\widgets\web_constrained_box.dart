import 'package:stackfood_multivendor/helper/responsive_helper.dart';
import 'package:flutter/material.dart';

class WebConstrained<PERSON>ox extends StatelessWidget {
  final int dataLength;
  final int minLength;
  final double minHeight;
  final Widget child ;
  const WebConstrainedBox({super.key, required this.dataLength, this.minLength = 5, this.minHeight = 0.6, required this.child});

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(minHeight: ResponsiveHelper.isDesktop(context) ? dataLength < minLength ? MediaQuery.of(context).size.height * minHeight: 0.0 : 0.0),
      child: child,
    );
  }
}
